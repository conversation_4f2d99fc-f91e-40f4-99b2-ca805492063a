# Deployment Guide

This document provides comprehensive instructions for deploying the Crypto Market Watcher monorepo in various environments.

## 📋 Prerequisites

### System Requirements
- **Node.js**: 18.x or higher
- **Yarn**: 1.22.x or higher
- **Docker**: 20.x or higher (for containerized deployment)
- **Docker Compose**: 2.x or higher

### External Services
- **Redis**: For real-time data caching and pub/sub
- **MongoDB**: For persistent data storage
- **Quidax API**: API key required for market data

## 🔧 Environment Configuration

### Required Environment Variables

Create appropriate `.env` files based on your deployment environment:

```bash
# Core Configuration
NODE_ENV=production
WORKER_PORT=3000
API_PORT=8001

# Database Configuration
REDIS_URL=redis://localhost:6379
MONGODB_URI=mongodb://localhost:27017/marketdata

# API Configuration
API_KEY=your_quidax_api_key_here
QUIDAX_BASE_URL=https://www.quidax.com/api/v1

# Worker Configuration
UPDATE_INTERVAL_MS=10000
REDIS_PUBLISH_INTERVAL_SECONDS=5
MONGO_SAVE_CRON=0 0 * * *

# Logging
LOG_LEVEL=info
```

### Environment Files Structure
```
.env                    # Default/fallback environment variables
.env.development        # Development-specific variables
.env.production         # Production-specific variables
```

## 🚀 Deployment Methods

### Method 1: Automated Deployment Script

The easiest way to deploy is using the provided deployment script:

```bash
# Production deployment
./scripts/deploy.sh production

# Development deployment
./scripts/deploy.sh development
```

### Method 2: Manual Docker Compose Deployment

#### Production Deployment
```bash
# 1. Set environment variables
export API_KEY="your_quidax_api_key"
export QUIDAX_BASE_URL="https://www.quidax.com/api/v1"

# 2. Deploy the stack
docker-compose up --build -d

# 3. Check service status
docker-compose ps

# 4. View logs
docker-compose logs -f
```

#### Development Deployment
```bash
# 1. Start development infrastructure
docker-compose -f docker-compose.dev.yml up -d

# 2. Install dependencies
yarn install

# 3. Build shared package
yarn workspace @crypto-watcher/shared build

# 4. Start services in development mode
yarn dev
```

### Method 3: Manual Build and Run

#### Build Process
```bash
# 1. Install dependencies
yarn install --frozen-lockfile

# 2. Build all packages
yarn build

# 3. Run type checking
yarn type-check
```

#### Start Services
```bash
# Start worker service
yarn workspace @crypto-watcher/worker start

# Start API service (in another terminal)
yarn workspace @crypto-watcher/api start
```

## 🐳 Docker Deployment Details

### Service Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Redis       │    │    MongoDB      │    │   Worker App    │
│   Port: 6379    │    │   Port: 27017   │    │   Port: 3000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │    API App      │
                                              │   Port: 8001    │
                                              └─────────────────┘
```

### Container Configuration

#### Worker Service
- **Image**: Built from `apps/worker/Dockerfile`
- **Port**: 3000
- **Dependencies**: Redis, MongoDB
- **Health Check**: HTTP GET `/health`

#### API Service
- **Image**: Built from `apps/api/Dockerfile`
- **Port**: 8001
- **Dependencies**: Redis, Worker
- **Health Check**: HTTP GET `/health`

#### Redis Service
- **Image**: `redis:7-alpine`
- **Port**: 6379
- **Persistence**: Volume mounted to `/data`

#### MongoDB Service
- **Image**: `mongo:6`
- **Port**: 27017
- **Persistence**: Volume mounted to `/data/db`

### Scaling Services

```bash
# Scale API service to 3 instances
docker-compose up --scale api=3 -d

# Scale worker service to 2 instances
docker-compose up --scale worker=2 -d
```

## 🔍 Health Monitoring

### Health Check Endpoints

#### Worker Service
```bash
curl http://localhost:3000/health
```

Response:
```json
{
  "status": "healthy",
  "service": "worker",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600
}
```

#### API Service
```bash
curl http://localhost:8001/health
```

Response:
```json
{
  "status": "healthy",
  "service": "api",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600
}
```

### Service Status Monitoring

```bash
# Check all services
docker-compose ps

# View service logs
docker-compose logs -f [service_name]

# Check resource usage
docker stats
```

## 🔧 Production Optimizations

### Performance Tuning

#### Redis Configuration
```bash
# Add to redis.conf for production
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

#### MongoDB Configuration
```bash
# Recommended MongoDB settings
# Add to mongod.conf
storage:
  wiredTiger:
    engineConfig:
      cacheSizeGB: 2
```

#### Node.js Optimization
```bash
# Environment variables for production
NODE_ENV=production
NODE_OPTIONS="--max-old-space-size=2048"
```

### Security Considerations

1. **Environment Variables**: Never commit sensitive data
2. **Network Security**: Use Docker networks for service isolation
3. **Access Control**: Implement proper authentication for MongoDB
4. **SSL/TLS**: Use HTTPS in production with reverse proxy
5. **Rate Limiting**: Implement rate limiting for API endpoints

## 🚨 Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using the ports
lsof -i :3000
lsof -i :8001
lsof -i :6379
lsof -i :27017

# Kill processes if needed
kill -9 <PID>
```

#### Service Connection Issues
```bash
# Test Redis connection
docker-compose exec redis redis-cli ping

# Test MongoDB connection
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')"

# Check service logs
docker-compose logs worker
docker-compose logs api
```

#### Build Issues
```bash
# Clean and rebuild
yarn clean
rm -rf node_modules yarn.lock
yarn install
yarn build
```

#### Memory Issues
```bash
# Check container memory usage
docker stats

# Increase memory limits in docker-compose.yml
services:
  worker:
    deploy:
      resources:
        limits:
          memory: 1G
```

### Log Analysis

```bash
# View real-time logs
docker-compose logs -f --tail=100

# Filter logs by service
docker-compose logs worker | grep ERROR

# Export logs to file
docker-compose logs > deployment.log
```

## 📊 Monitoring and Alerting

### Development Tools
- **Redis Commander**: http://localhost:8081
- **Mongo Express**: http://localhost:8082 (admin/admin123)

### Production Monitoring
Consider implementing:
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **ELK Stack**: Log aggregation and analysis
- **Uptime monitoring**: Service availability checks

## 🔄 CI/CD Integration

### GitHub Actions
The repository includes automated CI/CD workflows:

1. **Testing**: Runs on every push/PR
2. **Building**: Creates Docker images
3. **Deployment**: Deploys to production on main branch

### Manual Deployment Triggers
```bash
# Trigger deployment manually
gh workflow run ci-cd.yml
```

## 📝 Maintenance

### Regular Tasks

#### Daily
- Monitor service health
- Check error logs
- Verify data flow

#### Weekly
- Review performance metrics
- Update dependencies (if needed)
- Backup database

#### Monthly
- Security updates
- Performance optimization review
- Capacity planning

### Backup Strategy

```bash
# MongoDB backup
docker-compose exec mongodb mongodump --out /backup

# Redis backup
docker-compose exec redis redis-cli BGSAVE
```

## 🆘 Emergency Procedures

### Service Recovery
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart worker

# Full redeployment
docker-compose down
docker-compose up --build -d
```

### Data Recovery
```bash
# Restore MongoDB from backup
docker-compose exec mongodb mongorestore /backup

# Check data integrity
docker-compose exec mongodb mongosh --eval "db.marketdata.count()"
```

For additional support, refer to the main README.md or create an issue in the repository.
