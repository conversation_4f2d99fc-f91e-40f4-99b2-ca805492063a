# Quick Start Guide

Get the Crypto Market Watcher monorepo up and running in minutes!

## 🚀 One-Command Setup

### Prerequisites
- Node.js 18+
- Yarn
- Docker & Docker Compose

### Development Setup (5 minutes)

```bash
# 1. Clone and install
git clone <repository-url>
cd crypto-market-watcher
yarn install

# 2. Start infrastructure
docker-compose -f docker-compose.dev.yml up -d

# 3. Configure environment
cp .env.development .env
# Edit .env with your API_KEY

# 4. Build and start
yarn build
yarn dev
```

### Production Deployment (2 minutes)

```bash
# 1. Set environment variables
export API_KEY="your_quidax_api_key"
export QUIDAX_BASE_URL="https://www.quidax.com/api/v1"

# 2. Deploy
./scripts/deploy.sh production
```

## 🎯 What You Get

### Services Running
- **Worker Service**: http://localhost:3000 (Background data processing)
- **API Service**: http://localhost:8001 (Real-time WebSocket API)
- **Redis**: localhost:6379 (Data caching)
- **MongoDB**: localhost:27017 (Data storage)

### Development Tools
- **Redis Commander**: http://localhost:8081 (Redis management)
- **Mongo Express**: http://localhost:8082 (MongoDB management, admin/admin123)

## 🔧 Essential Commands

```bash
# Development
yarn dev                    # Start all services in development
yarn build                  # Build all packages
yarn clean                  # Clean build artifacts

# Individual services
yarn workspace @crypto-watcher/worker dev
yarn workspace @crypto-watcher/api dev
yarn workspace @crypto-watcher/shared build

# Docker
docker-compose up -d        # Start production stack
docker-compose logs -f      # View logs
docker-compose ps           # Check status
```

## 🧪 Testing the Setup

### 1. Check Service Health
```bash
# Worker health
curl http://localhost:3000/health

# API health  
curl http://localhost:8001/health
```

### 2. Test WebSocket Connection
```javascript
// In browser console or Node.js
const socket = io('http://localhost:8001');

// Subscribe to all markets
socket.emit('subscribeAllMarkets');
socket.on('allMarkets', (data) => {
  console.log('Market data:', data);
});

// Subscribe to specific market
socket.emit('subscribe', { event: 'market', pair: 'btc_ngn' });
socket.on('market:btc_ngn', (data) => {
  console.log('BTC/NGN data:', data);
});
```

### 3. Check Data Flow
```bash
# Check Redis for market data
docker-compose exec redis redis-cli keys "market:*"

# Check MongoDB for stored data
docker-compose exec mongodb mongosh marketdata --eval "db.marketdatas.count()"
```

## 🔍 Troubleshooting

### Port Conflicts
```bash
# Check what's using ports
lsof -i :3000 :8001 :6379 :27017

# Kill conflicting processes
kill -9 <PID>
```

### Service Issues
```bash
# Restart specific service
docker-compose restart worker
docker-compose restart api

# View service logs
docker-compose logs worker
docker-compose logs api
```

### Build Issues
```bash
# Clean rebuild
yarn clean
rm -rf node_modules yarn.lock
yarn install
yarn build
```

## 📊 Monitoring

### Real-time Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f worker
```

### Resource Usage
```bash
# Container stats
docker stats

# System resources
htop
```

## 🎉 Success Indicators

✅ **All services healthy**: Health endpoints return 200
✅ **Data flowing**: Redis contains market data keys
✅ **WebSocket working**: Browser console shows market updates
✅ **MongoDB storing**: Database contains market documents
✅ **No errors**: Clean logs without error messages

## 📚 Next Steps

1. **Explore the API**: Check out the WebSocket events in the README
2. **Customize Configuration**: Modify environment variables as needed
3. **Add Monitoring**: Set up Grafana dashboards for production
4. **Scale Services**: Use Docker Compose scaling for high load
5. **Deploy to Cloud**: Adapt the deployment for your cloud provider

## 🆘 Need Help?

- **Documentation**: Check README.md and DEPLOYMENT.md
- **Issues**: Create a GitHub issue
- **Logs**: Always check `docker-compose logs -f` first
- **Health**: Verify all health endpoints are responding

Happy coding! 🚀
