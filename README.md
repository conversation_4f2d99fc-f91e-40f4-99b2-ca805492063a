# Crypto Market Watcher - Monorepo

A real-time cryptocurrency market data monitoring system built with TypeScript, Node.js, Socket.IO, Redis, and MongoDB. This monorepo contains both the background worker service and the real-time API service.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Quidax API    │    │     Redis       │    │    MongoDB      │
│                 │    │   (Pub/Sub)     │    │   (Storage)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Worker Service │◄──►│  Shared Package │◄──►│   API Service   │
│                 │    │                 │    │                 │
│ • Fetch Data    │    │ • Interfaces    │    │ • Socket.IO     │
│ • Process Data  │    │ • Utilities     │    │ • Real-time     │
│ • Publish Redis │    │ • Config        │    │ • WebSocket     │
│ • Save MongoDB  │    │ • Redis Utils   │    │ • Client API    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Project Structure

```
crypto-market-watcher/
├── apps/
│   ├── worker/                 # Background worker service
│   │   ├── src/
│   │   │   ├── services/       # Business logic services
│   │   │   ├── workers/        # Cron job workers
│   │   │   └── server.ts       # Worker entry point
│   │   ├── Dockerfile
│   │   └── package.json
│   └── api/                    # Socket.IO API service
│       ├── src/
│       │   ├── services/       # API services
│       │   └── server.ts       # API entry point
│       ├── Dockerfile
│       └── package.json
├── packages/
│   └── shared/                 # Shared utilities and types
│       ├── src/
│       │   ├── interfaces/     # TypeScript interfaces
│       │   ├── utils/          # Utility functions
│       │   └── config/         # Configuration management
│       └── package.json
├── scripts/
│   └── deploy.sh              # Deployment script
├── .github/workflows/         # CI/CD pipelines
├── docker-compose.yml         # Production deployment
├── docker-compose.dev.yml     # Development environment
├── package.json              # Root package.json (workspace)
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- Yarn
- Docker & Docker Compose (for containerized deployment)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd crypto-market-watcher
   ```

2. **Install dependencies**
   ```bash
   yarn install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.development .env
   # Edit .env with your configuration
   ```

4. **Start development services (Redis & MongoDB)**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

5. **Build shared package**
   ```bash
   yarn workspace @crypto-watcher/shared build
   ```

6. **Start development servers**
   ```bash
   # Terminal 1: Start worker
   yarn workspace @crypto-watcher/worker dev
   
   # Terminal 2: Start API
   yarn workspace @crypto-watcher/api dev
   ```

### Production Deployment

1. **Using deployment script**
   ```bash
   ./scripts/deploy.sh production
   ```

2. **Manual Docker Compose**
   ```bash
   docker-compose up --build -d
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment mode | `development` | No |
| `WORKER_PORT` | Worker service port | `3000` | No |
| `API_PORT` | API service port | `8001` | No |
| `REDIS_URL` | Redis connection URL | `redis://localhost:6379` | No |
| `MONGODB_URI` | MongoDB connection URI | `mongodb://localhost:27017/marketdata` | No |
| `API_KEY` | Quidax API key | - | Yes |
| `QUIDAX_BASE_URL` | Quidax API base URL | `https://www.quidax.com/api/v1` | No |
| `UPDATE_INTERVAL_MS` | Data fetch interval | `10000` | No |
| `REDIS_PUBLISH_INTERVAL_SECONDS` | Redis publish interval | `5` | No |
| `MONGO_SAVE_CRON` | MongoDB save cron pattern | `0 0 * * *` | No |
| `LOG_LEVEL` | Logging level | `info` | No |

### Environment Files

- `.env` - Default environment variables
- `.env.development` - Development-specific variables
- `.env.production` - Production-specific variables

## 📦 Available Scripts

### Root Level
```bash
yarn build              # Build all packages
yarn dev                # Start all services in development
yarn start              # Start all services in production
yarn test               # Run tests for all packages
yarn clean              # Clean all build artifacts
yarn type-check         # Run TypeScript type checking
```

### Individual Services
```bash
yarn workspace @crypto-watcher/shared build
yarn workspace @crypto-watcher/worker dev
yarn workspace @crypto-watcher/api start
```

## 🐳 Docker Commands

### Development
```bash
# Start development infrastructure
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

### Production
```bash
# Deploy production stack
docker-compose up --build -d

# View service status
docker-compose ps

# View logs
docker-compose logs -f

# Scale services
docker-compose up --scale worker=2 --scale api=3 -d
```

## 🔍 Monitoring & Management

### Development Tools
- **Redis Commander**: http://localhost:8081 - Redis management interface
- **Mongo Express**: http://localhost:8082 - MongoDB management interface (admin/admin123)

### Service Endpoints
- **Worker Service**: http://localhost:3000
- **API Service**: http://localhost:8001

## 🧪 Testing

```bash
# Run all tests
yarn test

# Run tests for specific package
yarn workspace @crypto-watcher/worker test

# Run type checking
yarn type-check
```

## 📊 API Usage

### Socket.IO Events

#### Subscribe to Single Market
```javascript
socket.emit('subscribe', { event: 'market', pair: 'btc_ngn' });
socket.on('market:btc_ngn', (data) => {
  console.log('Market data:', data);
});
```

#### Subscribe to All Markets
```javascript
socket.emit('subscribeAllMarkets');
socket.on('allMarkets', (data) => {
  console.log('All markets:', data);
});
```

#### Unsubscribe
```javascript
socket.emit('unsubscribe:btc_ngn');
socket.emit('unsubscribeAllMarkets');
```

## 🚀 Deployment Strategies

### 1. Single Server Deployment
```bash
./scripts/deploy.sh production
```

### 2. Container Registry Deployment
```bash
# Build and push images
docker build -t your-registry/crypto-watcher-worker:latest -f apps/worker/Dockerfile .
docker build -t your-registry/crypto-watcher-api:latest -f apps/api/Dockerfile .

# Deploy to your infrastructure
kubectl apply -f k8s/
```

### 3. CI/CD Pipeline
The project includes GitHub Actions workflows for:
- Automated testing
- Docker image building
- Container registry publishing
- Deployment automation

## 🔧 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the ports
   lsof -i :3000
   lsof -i :8001
   ```

2. **Redis connection issues**
   ```bash
   # Test Redis connection
   redis-cli ping
   ```

3. **MongoDB connection issues**
   ```bash
   # Test MongoDB connection
   mongosh --eval "db.adminCommand('ping')"
   ```

4. **Build issues**
   ```bash
   # Clean and rebuild
   yarn clean
   yarn install
   yarn build
   ```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
