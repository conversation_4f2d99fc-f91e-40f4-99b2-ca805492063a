{"name": "@crypto-watcher/api", "version": "1.0.0", "description": "Crypto Market Watcher - Socket.IO API Service", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "echo 'Linting API...'", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@crypto-watcher/shared": "1.0.0", "express": "^4.21.2", "http": "^0.0.1-security", "socket.io": "^4.8.1"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "^22.13.14", "@types/socket.io": "^3.0.2", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "license": "MIT"}