import express from "express";
import http from "http";
import { Server } from "socket.io";
import {
  apiConfig as config,
  validateConfig,
  CombinedMarketData,
  SubscribeMessage,
  RedisUtils,
} from "@crypto-watcher/shared";
import { RedisPublisherService } from "./services/redis-publisher.service";

// Validate configuration
validateConfig();

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
});

const redisPublisherService = new RedisPublisherService();

// Health check endpoint
app.get("/health", (_, res) => {
  res.status(200).json({
    status: "healthy",
    service: "api",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

io.on("connection", (socket) => {
  console.log("New client connected:", socket.id);

  // Track subscriptions for cleanup
  const marketSubscriptions = new Map<string, () => Promise<void>>();
  let allMarketsUnsubscribe: (() => Promise<void>) | null = null;

  // Subscribe to single pair
  socket.on("subscribe", async (message: { event: string; pair: string }) => {
    const { event, pair } = message;
    const formattedPair = pair;

    if (marketSubscriptions.has(formattedPair)) {
      return;
    }

    console.log(`Client ${socket.id} subscribed to ${event}:${pair}`);

    // Initial data fetch
    const marketData = await redisPublisherService.getMarketData(formattedPair);

    socket.emit(`${event}:${pair}`, marketData);

    const unsubscribe = await RedisUtils.subscribeToMarket(
      formattedPair,
      (data: CombinedMarketData) => {
        socket.emit(`${event}:${pair}`, data);
      }
    );

    marketSubscriptions.set(formattedPair, unsubscribe);

    socket.on(`unsubscribe:${pair}`, async () => {
      const unsubscribe = marketSubscriptions.get(formattedPair);
      if (unsubscribe) {
        await unsubscribe();
        marketSubscriptions.delete(formattedPair);
      }
      socket.off(`unsubscribe:${pair}`, () => {});
    });
  });

  // Get all markets with periodic updates
  socket.on("subscribeAllMarkets", async () => {
    if (allMarketsUnsubscribe) {
      return; // Already subscribed
    }

    console.log(`Client ${socket.id} subscribed to all markets`);

    // Initial data fetch
    const allMarketData = await redisPublisherService.getMarketData();
    socket.emit("allMarkets", allMarketData);

    // Setup Redis Pub/Sub listener for all markets
    allMarketsUnsubscribe = await RedisUtils.subscribeToAllMarkets(
      (data: Record<string, CombinedMarketData>) => {
        socket.emit("allMarkets", data);
      }
    );

    // Cleanup on unsubscribe
    socket.on("unsubscribeAllMarkets", async () => {
      if (allMarketsUnsubscribe) {
        await allMarketsUnsubscribe();
        allMarketsUnsubscribe = null;
      }
      socket.off("unsubscribeAllMarkets", () => {});
    });
  });

  socket.on("disconnect", async () => {
    console.log(`Client ${socket.id} disconnected`);

    // Clean up individual market subscriptions
    for (const [, unsubscribe] of marketSubscriptions) {
      await unsubscribe();
    }
    marketSubscriptions.clear();

    // Clean up all markets subscription
    if (allMarketsUnsubscribe) {
      await allMarketsUnsubscribe();
      allMarketsUnsubscribe = null;
    }
  });
});

// Start the server
server.listen(config.PORT, () => {
  console.log(`IO server running on port ${config.PORT}`);
});

// Graceful shutdown
process.on("SIGINT", () => {
  console.log("Shutting down gracefully...");
  server.close(() => {
    console.log("Server closed");
    process.exit(0);
  });
});
