import {
  CombinedMarketData,
  RedisUtils,
  unflattenObject,
} from "@crypto-watcher/shared";

export class RedisPublisherService {
  async getMarketData(
    pair?: string
  ): Promise<CombinedMarketData | Record<string, CombinedMarketData>> {
    if (pair) {
      const data = await RedisUtils.getMarketData(pair);
      return unflattenObject(data) as CombinedMarketData;
    } else {
      const allData = await RedisUtils.getAllMarketData();
      const marketData: Record<string, CombinedMarketData> = {};

      for (const [pair, data] of Object.entries(allData)) {
        marketData[pair] = unflattenObject(data) as CombinedMarketData;
      }

      return marketData;
    }
  }
}
