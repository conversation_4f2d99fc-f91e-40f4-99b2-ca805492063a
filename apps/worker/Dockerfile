# Multi-stage build for Worker service
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package files for dependency installation
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
COPY packages/shared/package.json ./packages/shared/
COPY apps/worker/package.json ./apps/worker/

# Install dependencies
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Copy node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy all TypeScript configuration files
COPY tsconfig.json ./
COPY packages/shared/tsconfig.json ./packages/shared/
COPY apps/worker/tsconfig.json ./apps/worker/

# Copy package.json files
COPY package.json ./
COPY packages/shared/package.json ./packages/shared/
COPY apps/worker/package.json ./apps/worker/

# Copy source code
COPY packages/shared/src ./packages/shared/src
COPY apps/worker/src ./apps/worker/src

# Build packages in correct order with proper error handling
RUN set -e && \
  echo "Building shared package..." && \
  yarn workspace @crypto-watcher/shared build && \
  echo "Building worker package..." && \
  yarn workspace @crypto-watcher/worker build && \
  echo "Build completed successfully"

# Production image, copy all the files and run the app
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 worker

# Copy built application and necessary files
COPY --from=builder --chown=worker:nodejs /app/apps/worker/dist ./apps/worker/dist
COPY --from=builder --chown=worker:nodejs /app/packages/shared/dist ./packages/shared/dist
COPY --from=builder --chown=worker:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=worker:nodejs /app/package.json ./package.json
COPY --from=builder --chown=worker:nodejs /app/apps/worker/package.json ./apps/worker/package.json
COPY --from=builder --chown=worker:nodejs /app/packages/shared/package.json ./packages/shared/package.json

# Copy any additional runtime files that might be needed
COPY --from=builder --chown=worker:nodejs /app/apps/worker/src/docs ./apps/worker/src/docs

USER worker

EXPOSE 3000

ENV PORT=3000

CMD ["node", "apps/worker/dist/server.js"]
