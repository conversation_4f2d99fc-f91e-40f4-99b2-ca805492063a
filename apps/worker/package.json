{"name": "@crypto-watcher/worker", "version": "1.0.0", "description": "Crypto Market Watcher - Background Worker Service", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "echo 'Linting worker...'", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@crypto-watcher/shared": "1.0.0", "@types/js-yaml": "^4.0.9", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "axios": "^1.8.4", "cron": "^4.1.1", "express": "^4.21.2", "http": "^0.0.1-security", "js-yaml": "^4.1.0", "node-cron": "^3.0.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/cron": "^2.4.3", "@types/express": "^5.0.1", "@types/node": "^22.13.14", "@types/node-cron": "^3.0.11", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "license": "MIT"}