import swaggerJsdoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";
import { Express } from "express";
import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";

const openApiPath = path.join(__dirname, "../docs/openapi.yaml");
let openApiSpec: any;

try {
  const yamlContent = fs.readFileSync(openApiPath, "utf8");
  openApiSpec = yaml.load(yamlContent);
} catch (error) {
  console.error("Error loading OpenAPI specification:", error);
  openApiSpec = {
    openapi: "3.0.3",
    info: {
      title: "Market Data REST API",
      version: "1.0.0",
      description:
        "REST API for retrieving historical cryptocurrency market data",
    },
    servers: [
      {
        url: "http://localhost:3000/api/market-data",
        description: "Development server",
      },
    ],
  };
}

const swaggerUiOptions: swaggerUi.SwaggerUiOptions = {
  explorer: true,
  swaggerOptions: {
    docExpansion: "list",
    filter: true,
    showRequestDuration: true,
    tryItOutEnabled: true,
    requestInterceptor: (req: any) => {
      return req;
    },
    responseInterceptor: (res: any) => {
      return res;
    },
  },
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info { margin: 20px 0 }
    .swagger-ui .info .title { color: #3b4151 }
    .swagger-ui .scheme-container { background: #f7f7f7; padding: 15px; border-radius: 4px; margin: 20px 0 }
    .swagger-ui .opblock.opblock-get { border-color: #61affe; background: rgba(97,175,254,.1) }
    .swagger-ui .opblock.opblock-post { border-color: #49cc90; background: rgba(73,204,144,.1) }
    .swagger-ui .opblock.opblock-put { border-color: #fca130; background: rgba(252,161,48,.1) }
    .swagger-ui .opblock.opblock-delete { border-color: #f93e3e; background: rgba(249,62,62,.1) }
  `,
  customSiteTitle: "Market Data API Documentation",
  customfavIcon: "/favicon.ico",
};

export const setupSwagger = (app: Express): void => {
  app.use("/api/docs", swaggerUi.serve);
  app.get("/api/docs", swaggerUi.setup(openApiSpec, swaggerUiOptions));

  app.get("/api/docs/openapi.json", (req, res) => {
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(openApiSpec, null, 2));
  });

  app.get("/api/docs/openapi.yaml", (req, res) => {
    res.setHeader("Content-Type", "text/yaml");
    try {
      const yamlContent = fs.readFileSync(openApiPath, "utf8");
      res.send(yamlContent);
    } catch (error) {
      res
        .status(500)
        .json({ error: "Unable to load OpenAPI YAML specification" });
    }
  });

  app.get("/docs", (req, res) => {
    res.redirect("/api/docs");
  });

  console.log("📚 Swagger UI available at: http://localhost:3000/api/docs");
  console.log(
    "📄 OpenAPI JSON spec at: http://localhost:3000/api/docs/openapi.json"
  );
  console.log(
    "📄 OpenAPI YAML spec at: http://localhost:3000/api/docs/openapi.yaml"
  );
};

export { openApiSpec };

export const validateOpenApiSpec = (): boolean => {
  try {
    // Basic validation - check required fields
    if (!openApiSpec.openapi) {
      console.error('OpenAPI specification missing "openapi" field');
      return false;
    }

    if (!openApiSpec.info) {
      console.error('OpenAPI specification missing "info" field');
      return false;
    }

    if (!openApiSpec.info.title) {
      console.error('OpenAPI specification missing "info.title" field');
      return false;
    }

    if (!openApiSpec.info.version) {
      console.error('OpenAPI specification missing "info.version" field');
      return false;
    }

    if (!openApiSpec.paths) {
      console.error('OpenAPI specification missing "paths" field');
      return false;
    }

    console.log("✅ OpenAPI specification validation passed");
    return true;
  } catch (error) {
    console.error("❌ OpenAPI specification validation failed:", error);
    return false;
  }
};

export const getApiEndpointsSummary = (): string[] => {
  const endpoints: string[] = [];

  if (openApiSpec.paths) {
    Object.keys(openApiSpec.paths).forEach((path) => {
      const pathItem = openApiSpec.paths[path];
      Object.keys(pathItem).forEach((method) => {
        if (
          ["get", "post", "put", "delete", "patch"].includes(
            method.toLowerCase()
          )
        ) {
          const operation = pathItem[method];
          const summary = operation.summary || "No summary";
          endpoints.push(`${method.toUpperCase()} ${path} - ${summary}`);
        }
      });
    });
  }

  return endpoints;
};

export const printApiEndpoints = (): void => {
  console.log("\n📋 Available API Endpoints:");
  console.log("=".repeat(50));

  const endpoints = getApiEndpointsSummary();
  endpoints.forEach((endpoint) => {
    console.log(`  ${endpoint}`);
  });

  console.log("=".repeat(50));
  console.log(`Total endpoints: ${endpoints.length}\n`);
};
