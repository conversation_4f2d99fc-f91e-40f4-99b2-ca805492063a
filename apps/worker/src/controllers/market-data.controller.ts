import { Request, Response } from "express";
import { MongoWriterService } from "../services/mongo-writer.service";
import { ResponseUtils } from "../utils/response.utils";
import {
  HistoricalDataResponse,
  AggregatedDataResponse,
  LatestDataResponse,
  MarketStatistics,
  CurrencyPairsResponse,
  HealthCheckResponse,
} from "../interfaces/api";
import { MarketDataModel } from "@crypto-watcher/shared";

export class MarketDataController {
  private mongoWriterService: MongoWriterService;

  constructor() {
    this.mongoWriterService = new MongoWriterService();
  }

  /**
   * Get latest market data for a specific currency pair
   * GET /api/market-data/latest/:currencyPair
   */
  getLatestData = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { currencyPair } = req.params;

      const data = await this.mongoWriterService.getLatestData(currencyPair);

      const response: LatestDataResponse = {
        currencyPair,
        data,
        timestamp: new Date().toISOString(),
      };

      if (!data) {
        return ResponseUtils.notFound(
          res,
          `No data found for currency pair: ${currencyPair}`,
          "Latest data not available"
        );
      }

      return ResponseUtils.success(res, response, "Latest data retrieved successfully");
    } catch (error) {
      return ResponseUtils.handleError(res, error, "getLatestData");
    }
  });

  /**
   * Get historical market data
   * GET /api/market-data/historical?currencyPair=BTC/USDT&start=2024-01-01&end=2024-01-02&limit=100
   */
  getHistoricalData = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { currencyPair, startDate, endDate, limit } = req.validatedData;

      const data = await this.mongoWriterService.getHistoricalData(
        currencyPair,
        startDate,
        endDate,
        limit
      );

      const response: HistoricalDataResponse = {
        currencyPair,
        data,
        count: data.length,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
        },
      };

      return ResponseUtils.success(
        res,
        response,
        "Historical data retrieved successfully",
        data.length
      );
    } catch (error) {
      return ResponseUtils.handleError(res, error, "getHistoricalData");
    }
  });

  /**
   * Get aggregated market data
   * GET /api/market-data/aggregated?currencyPair=BTC/USDT&start=2024-01-01&end=2024-01-02&interval=hour
   */
  getAggregatedData = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { currencyPair, startDate, endDate, interval } = req.validatedData;

      const data = await this.mongoWriterService.getAggregatedData(
        currencyPair,
        startDate,
        endDate,
        interval
      );

      const response: AggregatedDataResponse = {
        currencyPair,
        interval,
        data,
        count: data.length,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
        },
      };

      return ResponseUtils.success(
        res,
        response,
        "Aggregated data retrieved successfully",
        data.length
      );
    } catch (error) {
      return ResponseUtils.handleError(res, error, "getAggregatedData");
    }
  });

  /**
   * Get market data by date range (flexible query)
   * GET /api/market-data/range?start=2024-01-01&end=2024-01-02&currencyPair=BTC/USDT&limit=100&sortOrder=desc
   */
  getDataByRange = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { currencyPair, start, end, limit, sortOrder } = req.query;

      const query: any = {
        timestamp: {
          $gte: new Date(start as string),
          $lte: new Date(end as string),
        },
      };

      if (currencyPair) {
        query.currencyPair = currencyPair;
      }

      const sort: any = { timestamp: sortOrder === "desc" ? -1 : 1 };

      let queryBuilder = MarketDataModel.find(query).sort(sort);

      if (limit) {
        queryBuilder = queryBuilder.limit(parseInt(limit as string, 10));
      }

      const data = await queryBuilder.lean().exec();

      const response = {
        data,
        count: data.length,
        filters: {
          currencyPair: currencyPair || "all",
          dateRange: {
            start: start as string,
            end: end as string,
          },
          limit: limit ? parseInt(limit as string, 10) : undefined,
          sortOrder: sortOrder || "asc",
        },
      };

      return ResponseUtils.success(
        res,
        response,
        "Range data retrieved successfully",
        data.length
      );
    } catch (error) {
      return ResponseUtils.handleError(res, error, "getDataByRange");
    }
  });

  /**
   * Get market statistics for a currency pair
   * GET /api/market-data/statistics/:currencyPair?period=24h
   */
  getMarketStatistics = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { currencyPair } = req.params;
      const { period = "24h" } = req.query;

      // Calculate date range based on period
      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case "24h":
          startDate.setHours(startDate.getHours() - 24);
          break;
        case "7d":
          startDate.setDate(startDate.getDate() - 7);
          break;
        case "30d":
          startDate.setDate(startDate.getDate() - 30);
          break;
        default:
          return ResponseUtils.badRequest(res, "Invalid period. Use 24h, 7d, or 30d");
      }

      // Get aggregated statistics
      const stats = await MarketDataModel.aggregate([
        {
          $match: {
            currencyPair,
            timestamp: { $gte: startDate, $lte: endDate },
            lastPrice: { $exists: true, $ne: null },
          },
        },
        {
          $project: {
            lastPrice: { $toDouble: "$lastPrice" },
            baseVolume: { $toDouble: "$baseVolume" },
            timestamp: 1,
          },
        },
        {
          $group: {
            _id: null,
            totalRecords: { $sum: 1 },
            minPrice: { $min: "$lastPrice" },
            maxPrice: { $max: "$lastPrice" },
            avgPrice: { $avg: "$lastPrice" },
            totalVolume: { $sum: "$baseVolume" },
            avgVolume: { $avg: "$baseVolume" },
            minVolume: { $min: "$baseVolume" },
            maxVolume: { $max: "$baseVolume" },
            firstPrice: { $first: "$lastPrice" },
            lastPrice: { $last: "$lastPrice" },
          },
        },
      ]);

      if (!stats.length) {
        return ResponseUtils.notFound(
          res,
          `No statistics available for ${currencyPair} in the ${period} period`
        );
      }

      const stat = stats[0];
      const priceChange = stat.lastPrice - stat.firstPrice;
      const priceChangePercent = ((priceChange / stat.firstPrice) * 100);

      const response: MarketStatistics = {
        currencyPair,
        period: period as string,
        totalRecords: stat.totalRecords,
        priceStats: {
          current: stat.lastPrice,
          min: stat.minPrice,
          max: stat.maxPrice,
          average: stat.avgPrice,
          change: priceChange,
          changePercent: priceChangePercent,
        },
        volumeStats: {
          total: stat.totalVolume,
          average: stat.avgVolume,
          min: stat.minVolume,
          max: stat.maxVolume,
        },
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
        },
      };

      return ResponseUtils.success(res, response, "Market statistics retrieved successfully");
    } catch (error) {
      return ResponseUtils.handleError(res, error, "getMarketStatistics");
    }
  });

  /**
   * Get available currency pairs
   * GET /api/market-data/pairs
   */
  getCurrencyPairs = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      const pairs = await MarketDataModel.distinct("currencyPair");

      const response: CurrencyPairsResponse = {
        pairs: pairs.sort(),
        count: pairs.length,
        lastUpdated: new Date().toISOString(),
      };

      return ResponseUtils.success(res, response, "Currency pairs retrieved successfully");
    } catch (error) {
      return ResponseUtils.handleError(res, error, "getCurrencyPairs");
    }
  });

  /**
   * Enhanced health check
   * GET /api/market-data/health
   */
  getHealthCheck = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      // Test MongoDB connection
      let mongoStatus: "connected" | "disconnected" = "disconnected";
      try {
        await MarketDataModel.findOne().limit(1);
        mongoStatus = "connected";
      } catch (error) {
        console.error("MongoDB health check failed:", error);
      }

      // Test Redis connection (if available)
      let redisStatus: "connected" | "disconnected" = "connected"; // Assume connected for now

      const response: HealthCheckResponse = {
        status: mongoStatus === "connected" ? "healthy" : "unhealthy",
        service: "market-data-api",
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: {
          mongodb: mongoStatus,
          redis: redisStatus,
        },
        version: "1.0.0",
      };

      const statusCode = response.status === "healthy" ? 200 : 503;
      return res.status(statusCode).json({
        success: response.status === "healthy",
        data: response,
        timestamp: response.timestamp,
      });
    } catch (error) {
      return ResponseUtils.handleError(res, error, "getHealthCheck");
    }
  });
}
