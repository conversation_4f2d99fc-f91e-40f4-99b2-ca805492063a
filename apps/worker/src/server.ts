import express from "express";
import * as http from "http";
import {
  workerConfig as config,
  connectMongoDB,
  validateConfig,
} from "@crypto-watcher/shared";
import { RedisPublisherWorker } from "./workers/redis-publisher.worker";
import { MongoWriterWorker } from "./workers/mongo-writer.worker";
import { marketDataRoutes } from "./routes/market-data.routes";
import {
  setupSwagger,
  validateOpenApiSpec,
  printApiEndpoints,
} from "./config/swagger";

validateConfig();

const app = express();
const server = http.createServer(app);

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Setup Swagger documentation
setupSwagger(app);

app.use("/api/market-data", marketDataRoutes);

app.get("/health", (_, res) => {
  res.status(200).json({
    status: "healthy",
    service: "worker",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

const redisPublisherWorker = new RedisPublisherWorker();
const mongoWriterWorker = new MongoWriterWorker();

connectMongoDB();

// Validate OpenAPI specification
if (validateOpenApiSpec()) {
  console.log("✅ OpenAPI specification is valid");
} else {
  console.warn("⚠️  OpenAPI specification validation failed");
}

redisPublisherWorker.start();
mongoWriterWorker.start();

server.listen(config.PORT, () => {
  console.log(`Worker server running on port ${config.PORT}`);
  console.log(`📚 API Documentation: http://localhost:${config.PORT}/api/docs`);

  // Print available endpoints in development
  if (process.env.NODE_ENV === "development" || !process.env.NODE_ENV) {
    printApiEndpoints();
  }
});

process.on("SIGINT", () => {
  console.log("Shutting down gracefully...");
  redisPublisherWorker.stop();
  mongoWriterWorker.stop();
  server.close(() => {
    console.log("Server closed");
    process.exit(0);
  });
});
