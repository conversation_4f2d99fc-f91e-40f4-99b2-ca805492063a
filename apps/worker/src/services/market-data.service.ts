import axios from "axios";
import {
  workerConfig as config,
  MarketData,
  MarketStats,
  CombinedMarketData,
  generateHash,
} from "@crypto-watcher/shared";

export class MarketDataService {
  private readonly BASE_URL = config.QUIDAX_BASE_URL;

  async fetchMarketData(): Promise<{
    marketTickerData: Record<string, MarketData>;
    marketSummaryData: Record<string, MarketStats>;
  }> {
    try {
      const [marketTickerData, marketSummaryData] = await Promise.all([
        axios.get(`${this.BASE_URL}/markets/tickers`, {
          headers: { Authorization: `Bearer ${config.API_KEY}` },
        }),
        axios.get(`${this.BASE_URL}/markets/summary/`, {
          headers: { Authorization: `Bearer ${config.API_KEY}` },
        }),
      ]);

      return {
        marketTickerData: marketTickerData.data.data as Record<
          string,
          MarketData
        >,
        marketSummaryData: marketSummaryData.data.data as Record<
          string,
          MarketStats
        >,
      };
    } catch (error) {
      throw new Error(
        `Failed to fetch market data: ${(error as Error).message}`
      );
    }
  }

  combineMarketData(
    marketTickerData: Record<string, MarketData>,
    marketSummaryData: Record<string, MarketStats>
  ): Record<string, CombinedMarketData> {
    const combinedMarketData: Record<string, CombinedMarketData> = {};

    // Process Market Tickers
    for (const [key, value] of Object.entries(marketTickerData)) {
      const formattedKey = key.replace(/_/g, "").toLowerCase();
      combinedMarketData[formattedKey] = {
        currencyPair: key,
        buy: value.ticker.buy,
        sell: value.ticker.sell,
        low: value.ticker.low,
        high: value.ticker.high,
        open: value.ticker.open,
        last: value.ticker.last,
        vol: value.ticker.vol,
      } as CombinedMarketData;
    }

    // Process Market Summaries and Merge with Market Tickers
    for (const [key, value] of Object.entries(marketSummaryData)) {
      const formattedKey = key.replace(/_/g, "").toLowerCase();

      if (!combinedMarketData[formattedKey]) {
        combinedMarketData[formattedKey] = {
          currencyPair: key,
        } as CombinedMarketData;
      }

      combinedMarketData[formattedKey] = {
        ...combinedMarketData[formattedKey],
        lastPrice: value.last_price,
        lowestAsk: value.lowest_ask,
        highestBid: value.highest_bid,
        baseVolume: value.base_volume,
        quoteVolume: value.quote_volume,
        priceChangePercent24h: value.price_change_percent_24h,
        highestPrice24h: value.highest_price_24h,
        lowestPrice24h: value.lowest_price_24h,
        status: value.price_change_percent_24h.startsWith("-")
          ? "loser"
          : value.price_change_percent_24h === "0"
          ? "no change"
          : "gainer",
      };
    }

    return combinedMarketData;
  }
}
