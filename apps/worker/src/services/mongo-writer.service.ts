import {
  CombinedMarketData,
  AggregatedMarketData,
  MarketDataModel,
} from "@crypto-watcher/shared";

export class MongoWriterService {
  constructor() {
    // Using shared MarketDataModel
  }

  /**
   * Insert market data in bulk
   * @param data Record of market data by currency pair
   * @param timestamp Optional timestamp (defaults to now)
   */
  public async insertMarketData(
    data: Record<string, CombinedMarketData>,
    timestamp?: Date
  ): Promise<number> {
    try {
      const now = timestamp || new Date();
      const documents = Object.values(data).map((item) => ({
        ...item,
        timestamp: now,
      }));

      if (documents.length === 0) return 0;

      const result = await MarketDataModel.insertMany(documents, {
        ordered: false, // Continue on error
      });

      return result.length;
    } catch (error) {
      if (error instanceof Error && error.message.includes("E11000")) {
        // Duplicate key errors are acceptable for time series data
        return 0;
      }
      console.error("Error inserting market data:", error);
      throw error;
    }
  }

  /**
   * Get historical market data for a specific pair
   * @param currencyPair The trading pair (e.g., "BTC/USDT")
   * @param start Start date
   * @param end End date
   * @param limit Maximum number of results
   */
  public async getHistoricalData(
    currencyPair: string,
    start: Date,
    end: Date,
    limit?: number
  ): Promise<any[]> {
    try {
      const query = MarketDataModel.find({
        currencyPair,
        timestamp: { $gte: start, $lte: end },
      }).sort({ timestamp: 1 });

      if (limit) {
        query.limit(limit);
      }

      return await query.lean().exec();
    } catch (error) {
      console.error("Error fetching historical data:", error);
      throw error;
    }
  }

  /**
   * Get aggregated market data
   * @param currencyPair The trading pair
   * @param start Start date
   * @param end End date
   * @param interval Aggregation interval
   */
  public async getAggregatedData(
    currencyPair: string,
    start: Date,
    end: Date,
    interval: "minute" | "hour" | "day" | "week" | "month"
  ): Promise<AggregatedMarketData[]> {
    try {
      const formatMap = {
        minute: "%Y-%m-%d %H:%M:00",
        hour: "%Y-%m-%d %H:00:00",
        day: "%Y-%m-%d 00:00:00",
        week: "%Y-%U", // ISO week number
        month: "%Y-%m-01 00:00:00",
      };

      const aggregation = await MarketDataModel.aggregate([
        {
          $match: {
            currencyPair,
            timestamp: { $gte: start, $lte: end },
            lastPrice: { $exists: true, $ne: null },
          },
        },
        {
          $project: {
            lastPrice: { $toDouble: "$lastPrice" },
            baseVolume: { $toDouble: "$baseVolume" },
            timestamp: 1,
          },
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: formatMap[interval],
                date: "$timestamp",
              },
            },
            averagePrice: { $avg: "$lastPrice" },
            minPrice: { $min: "$lastPrice" },
            maxPrice: { $max: "$lastPrice" },
            totalVolume: { $sum: "$baseVolume" },
            count: { $sum: 1 },
            firstPrice: { $first: "$lastPrice" },
            lastPrice: { $last: "$lastPrice" },
          },
        },
        { $sort: { _id: 1 } },
      ]);

      return aggregation.map((item) => ({
        period: item._id,
        averagePrice: item.averagePrice,
        minPrice: item.minPrice,
        maxPrice: item.maxPrice,
        totalVolume: item.totalVolume,
        count: item.count,
        firstPrice: item.firstPrice,
        lastPrice: item.lastPrice,
      }));
    } catch (error) {
      console.error("Error aggregating market data:", error);
      throw error;
    }
  }

  /**
   * Get the latest data point for a currency pair
   * @param currencyPair The trading pair
   */
  public async getLatestData(currencyPair: string): Promise<any> {
    try {
      return await MarketDataModel.findOne({ currencyPair })
        .sort({ timestamp: -1 })
        .lean()
        .exec();
    } catch (error) {
      console.error("Error fetching latest data:", error);
      throw error;
    }
  }
}
