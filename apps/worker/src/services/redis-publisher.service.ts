import {
  redis,
  REDIS_KEYS,
  RedisUtils,
  CombinedMarketData,
  generateHash,
  flattenObject,
  unflattenObject,
} from "@crypto-watcher/shared";

export class RedisPublisherService {
  async publishMarketData(
    combinedMarketData: Record<string, CombinedMarketData>
  ): Promise<void> {
    const pipeline = redis.pipeline();
    const marketHashes = new Set<string>();

    for (const [key, data] of Object.entries(combinedMarketData)) {
      const hashKey = REDIS_KEYS.MARKET_DATA(key);
      const hash = generateHash(data);

      if (!(await redis.sismember("crypto:market_hashes", hash))) {
        pipeline.hmset(hashKey, flattenObject(data));
        pipeline.sadd("crypto:market_hashes", hash);

        // Publish individual market update
        pipeline.publish(REDIS_KEYS.MARKET_CHANNEL(key), JSON.stringify(data));
      }
      marketHashes.add(hash);
    }

    // Publish all markets update
    pipeline.publish(
      REDIS_KEYS.ALL_MARKETS_CHANNEL,
      JSON.stringify(combinedMarketData)
    );

    // Clean up old hashes
    const allHashes = await redis.smembers("crypto:market_hashes");
    for (const oldHash of allHashes) {
      if (!marketHashes.has(oldHash)) {
        pipeline.srem("crypto:market_hashes", oldHash);
      }
    }

    await pipeline.exec();
  }

  async getMarketData(
    pair?: string
  ): Promise<CombinedMarketData | Record<string, CombinedMarketData>> {
    if (pair) {
      const data = await RedisUtils.getMarketData(pair);
      return unflattenObject(data) as CombinedMarketData;
    } else {
      const allData = await RedisUtils.getAllMarketData();
      const marketData: Record<string, CombinedMarketData> = {};

      for (const [pair, data] of Object.entries(allData)) {
        marketData[pair] = unflattenObject(data) as CombinedMarketData;
      }

      return marketData;
    }
  }
}
