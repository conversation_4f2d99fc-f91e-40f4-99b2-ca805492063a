import cron from "node-cron";
import { config } from "@crypto-watcher/shared";
import { MarketDataService } from "../services/market-data.service";
import { MongoWriterService } from "../services/mongo-writer.service";

export class MongoWriterWorker {
  private cronJob: cron.ScheduledTask | null = null;
  private marketDataService = new MarketDataService();
  private mongoWriterService = new MongoWriterService();

  start(): void {
    // Schedule the cron job based on configuration
    this.cronJob = cron.schedule(config.MONGO_SAVE_CRON, async () => {
      try {
        const { marketTickerData, marketSummaryData } =
          await this.marketDataService.fetchMarketData();
        const combinedMarketData = this.marketDataService.combineMarketData(
          marketTickerData,
          marketSummaryData
        );
        await this.mongoWriterService.insertMarketData(combinedMarketData);
        console.log("Market data saved to MongoDB");
      } catch (error) {
        console.error("Error in MongoDB writer worker:", error);
      }
    });

    console.log("MongoWriterWorker cron job started.");
  }

  stop(): void {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      console.log("MongoWriterWorker cron job stopped.");
    }
  }
}
