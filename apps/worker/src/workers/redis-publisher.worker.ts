// src/workers/redis-publisher.worker.ts
import cron from "node-cron";
import { config } from "@crypto-watcher/shared";
import { MarketDataService } from "../services/market-data.service";
import { RedisPublisherService } from "../services/redis-publisher.service";

export class RedisPublisherWorker {
  private marketDataService = new MarketDataService();
  private redisPublisherService = new RedisPublisherService();
  private cronJob: cron.ScheduledTask | null = null;
  private isRunning = false;

  async start(): Promise<void> {
    if (this.isRunning) {
      console.warn("Worker is already running");
      return;
    }

    this.isRunning = true;
    console.log("Starting Redis Publisher Worker");

    // Schedule based on configuration
    const cronExpression = `*/${config.REDIS_PUBLISH_INTERVAL_SECONDS} * * * * *`;
    this.cronJob = cron.schedule(cronExpression, async () => {
      try {
        const { marketTickerData, marketSummaryData } =
          await this.marketDataService.fetchMarketData();
        const combinedMarketData = this.marketDataService.combineMarketData(
          marketTickerData,
          marketSummaryData
        );

        // Update Redis and publish updates
        await this.redisPublisherService.publishMarketData(combinedMarketData);

        console.log("Market data published and notifications sent");
      } catch (error) {
        console.error("Error in Redis publisher worker:", error);
      }
    });

    // Initial immediate execution
    try {
      const { marketTickerData, marketSummaryData } =
        await this.marketDataService.fetchMarketData();
      const combinedMarketData = this.marketDataService.combineMarketData(
        marketTickerData,
        marketSummaryData
      );

      await this.redisPublisherService.publishMarketData(combinedMarketData);
    } catch (error) {
      console.error("Error in initial Redis publisher execution:", error);
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.warn("Worker is not running");
      return;
    }

    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
    }

    this.isRunning = false;
    console.log("Redis Publisher Worker stopped");
  }

  get status(): { isRunning: boolean } {
    return { isRunning: this.isRunning };
  }
}
