version: '3.8'

services:
  # Redis service for development
  redis:
    image: redis:7-alpine
    container_name: crypto-watcher-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes

  # MongoDB service for development
  mongodb:
    image: mongo:6
    container_name: crypto-watcher-mongodb-dev
    restart: unless-stopped
    ports:
      - "27017:27017"
    volumes:
      - mongodb_dev_data:/data/db
    environment:
      MONGO_INITDB_DATABASE: marketdata

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: crypto-watcher-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis

  # Mongo Express for MongoDB management
  mongo-express:
    image: mongo-express:latest
    container_name: crypto-watcher-mongo-express
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      - mongodb

volumes:
  redis_dev_data:
    driver: local
  mongodb_dev_data:
    driver: local

networks:
  default:
    name: crypto-watcher-dev-network
