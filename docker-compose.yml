version: '3.8'

services:
  # Redis service
  redis:
    image: redis:7-alpine
    container_name: crypto-watcher-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB service
  mongodb:
    image: mongo:6
    container_name: crypto-watcher-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_DATABASE: marketdata
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Worker service
  worker:
    build:
      context: .
      dockerfile: apps/worker/Dockerfile
    container_name: crypto-watcher-worker
    restart: unless-stopped
    environment:
      NODE_ENV: production
      WORKER_PORT: 3000
      API_PORT: 8001
      REDIS_URL: redis://redis:6379
      MONGODB_URI: mongodb://mongodb:27017/marketdata
      API_KEY: ${API_KEY}
      QUIDAX_BASE_URL: ${QUIDAX_BASE_URL}
      UPDATE_INTERVAL_MS: ${UPDATE_INTERVAL_MS:-10000}
      REDIS_PUBLISH_INTERVAL_SECONDS: ${REDIS_PUBLISH_INTERVAL_SECONDS:-5}
      MONGO_SAVE_CRON: ${MONGO_SAVE_CRON:-0 0 * * *}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    depends_on:
      redis:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    ports:
      - "3000:3000"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API service
  api:
    build:
      context: .
      dockerfile: apps/api/Dockerfile
    container_name: crypto-watcher-api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      WORKER_PORT: 3000
      API_PORT: 8001
      REDIS_URL: redis://redis:6379
      MONGODB_URI: mongodb://mongodb:27017/marketdata
      API_KEY: ${API_KEY}
      QUIDAX_BASE_URL: ${QUIDAX_BASE_URL}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    depends_on:
      redis:
        condition: service_healthy
      worker:
        condition: service_healthy
    ports:
      - "8001:8001"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
    driver: local
  mongodb_data:
    driver: local

networks:
  default:
    name: crypto-watcher-network
