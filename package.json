{"name": "crypto-market-watcher", "version": "1.0.0", "description": "Crypto Market Watcher - Monorepo with Worker and API services", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "yarn workspaces run build", "build:worker": "yarn workspace @crypto-watcher/worker build", "build:api": "yarn workspace @crypto-watcher/api build", "build:shared": "yarn workspace @crypto-watcher/shared build", "dev": "concurrently \"yarn dev:worker\" \"yarn dev:api\"", "dev:worker": "yarn workspace @crypto-watcher/worker dev", "dev:api": "yarn workspace @crypto-watcher/api dev", "start": "concurrently \"yarn start:worker\" \"yarn start:api\"", "start:worker": "yarn workspace @crypto-watcher/worker start", "start:api": "yarn workspace @crypto-watcher/api start", "test": "yarn workspaces run test", "test:worker": "yarn workspace @crypto-watcher/worker test", "test:api": "yarn workspace @crypto-watcher/api test", "clean": "yarn workspaces run clean && rm -rf node_modules", "lint": "yarn workspaces run lint", "type-check": "yarn workspaces run type-check", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"@types/node": "^22.13.14", "concurrently": "^9.1.2", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "dependencies": {"dotenv": "^16.4.7"}, "engines": {"node": ">=18.0.0", "yarn": ">=1.22.0"}, "license": "MIT"}