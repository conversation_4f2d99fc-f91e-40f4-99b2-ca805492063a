{"name": "@crypto-watcher/shared", "version": "1.0.0", "description": "Shared utilities, interfaces, and configurations for Crypto Market Watcher", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "echo 'Linting shared package...'"}, "dependencies": {"dotenv": "^16.4.7", "ioredis": "^5.6.0", "mongoose": "^8.13.0"}, "devDependencies": {"@types/node": "^22.13.14", "typescript": "^5.8.2"}, "files": ["dist/**/*"], "publishConfig": {"access": "restricted"}}