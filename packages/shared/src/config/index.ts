import * as dotenv from "dotenv";
import * as path from "path";

function findRootDir(): string {
  let currentDir = __dirname;
  while (currentDir !== path.dirname(currentDir)) {
    const packageJsonPath = path.join(currentDir, "package.json");
    try {
      const packageJson = require(packageJsonPath);
      if (packageJson.workspaces) {
        return currentDir;
      }
    } catch (e) {}
    currentDir = path.dirname(currentDir);
  }
  return process.cwd();
}

const rootDir = findRootDir();

const envFile =
  process.env.NODE_ENV === "production"
    ? ".env.production"
    : process.env.NODE_ENV === "development"
    ? ".env.development"
    : ".env";

dotenv.config({ path: path.resolve(rootDir, envFile) });
dotenv.config({ path: path.resolve(rootDir, ".env") });

export interface AppConfig {
  NODE_ENV: string;

  WORKER_PORT: number;
  API_PORT: number;

  REDIS_URL: string;

  MONGODB_URI: string;

  API_KEY: string;
  QUIDAX_BASE_URL: string;

  UPDATE_INTERVAL_MS: number;
  REDIS_PUBLISH_INTERVAL_SECONDS: number;
  MONGO_SAVE_CRON: string;

  LOG_LEVEL: string;
}

export const config: AppConfig = {
  NODE_ENV: process.env.NODE_ENV || "development",

  WORKER_PORT: parseInt(process.env.WORKER_PORT || "3000", 10),
  API_PORT: parseInt(process.env.API_PORT || "8001", 10),

  REDIS_URL: process.env.REDIS_URL || "redis://localhost:6379",

  MONGODB_URI:
    process.env.MONGODB_URI || "mongodb://localhost:27017/marketdata",

  API_KEY: process.env.API_KEY || "",
  QUIDAX_BASE_URL:
    process.env.QUIDAX_BASE_URL || "https://www.quidax.com/api/v1",

  UPDATE_INTERVAL_MS: parseInt(process.env.UPDATE_INTERVAL_MS || "10000", 10),
  REDIS_PUBLISH_INTERVAL_SECONDS: parseInt(
    process.env.REDIS_PUBLISH_INTERVAL_SECONDS || "5",
    10
  ),
  MONGO_SAVE_CRON: process.env.MONGO_SAVE_CRON || "0 0 * * *",

  LOG_LEVEL: process.env.LOG_LEVEL || "info",
};

export function validateConfig(): void {
  const requiredFields: (keyof AppConfig)[] = [];

  if (config.NODE_ENV === "production") {
    requiredFields.push(
      "REDIS_URL",
      "MONGODB_URI",
      "API_KEY",
      "QUIDAX_BASE_URL"
    );
  } else {
    requiredFields.push("QUIDAX_BASE_URL");
  }

  const missingFields = requiredFields.filter((field) => !config[field]);

  if (missingFields.length > 0) {
    if (config.NODE_ENV === "production") {
      throw new Error(
        `Missing required environment variables: ${missingFields.join(", ")}`
      );
    } else {
      console.warn(
        `Missing environment variables: ${missingFields.join(
          ", "
        )} - using defaults for development`
      );
    }
  }
}

export const workerConfig = {
  PORT: config.WORKER_PORT,
  REDIS_URL: config.REDIS_URL,
  MONGODB_URI: config.MONGODB_URI,
  QUIDAX_BASE_URL: config.QUIDAX_BASE_URL,
  API_KEY: config.API_KEY,
  UPDATE_INTERVAL_MS: config.UPDATE_INTERVAL_MS,
};

export const apiConfig = {
  PORT: config.API_PORT,
  REDIS_URL: config.REDIS_URL,
};
