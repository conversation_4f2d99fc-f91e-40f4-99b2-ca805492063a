import Redis from "ioredis";
import { config } from "./index";

const redis = new Redis(config.REDIS_URL, {
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
  lazyConnect: true,
  reconnectOnError: (err) => {
    const targetError = "READONLY";
    return err.message.includes(targetError);
  },
});

redis.on("connect", () => {
  console.log("Redis connected successfully");
});

redis.on("ready", () => {
  console.log("Redis is ready to accept commands");
});

redis.on("error", (err) => {
  console.error("Redis connection error:", err);
});

redis.on("close", () => {
  console.log("Redis connection closed");
});

redis.on("reconnecting", () => {
  console.log("Redis reconnecting...");
});

export default redis;

export const REDIS_KEYS = {
  MARKET_DATA: (pair: string) => `crypto:market:data:${pair}`,
  MARKET_CHANNEL: (pair: string) => `crypto:market:channel:${pair}`,
  ALL_MARKETS_CHANNEL: "crypto:market:channel:all",
  MARKET_LIST: "crypto:market:list",
  MARKET_STATS: (pair: string) => `crypto:market:stats:${pair}`,
} as const;

export class RedisUtils {
  static async setMarketData(
    pair: string,
    data: Record<string, any>
  ): Promise<void> {
    const key = REDIS_KEYS.MARKET_DATA(pair);
    await redis.hset(key, data);
  }

  static async getMarketData(pair: string): Promise<Record<string, string>> {
    const key = REDIS_KEYS.MARKET_DATA(pair);
    return await redis.hgetall(key);
  }

  static async getAllMarketData(): Promise<
    Record<string, Record<string, string>>
  > {
    const keys = await redis.keys("crypto:market:data:*");
    const marketData: Record<string, Record<string, string>> = {};

    for (const key of keys) {
      const pair = key.replace("crypto:market:data:", "");
      const data = await redis.hgetall(key);
      marketData[pair] = data;
    }

    return marketData;
  }

  static async publishMarketUpdate(pair: string, data: any): Promise<void> {
    const channel = REDIS_KEYS.MARKET_CHANNEL(pair);
    await redis.publish(channel, JSON.stringify(data));
  }

  static async publishAllMarketsUpdate(data: any): Promise<void> {
    const channel = REDIS_KEYS.ALL_MARKETS_CHANNEL;
    await redis.publish(channel, JSON.stringify(data));
  }

  static async subscribeToMarket(
    pair: string,
    callback: (data: any) => void
  ): Promise<() => Promise<void>> {
    const subscriber = new Redis(config.REDIS_URL);
    const channel = REDIS_KEYS.MARKET_CHANNEL(pair);

    await subscriber.subscribe(channel);

    subscriber.on("message", (receivedChannel, message) => {
      if (receivedChannel === channel) {
        try {
          const data = JSON.parse(message);
          callback(data);
        } catch (error) {
          console.error("Error parsing Redis message:", error);
        }
      }
    });

    return async () => {
      await subscriber.unsubscribe(channel);
      subscriber.disconnect();
    };
  }

  static async subscribeToAllMarkets(
    callback: (data: any) => void
  ): Promise<() => Promise<void>> {
    const subscriber = new Redis(config.REDIS_URL);
    const channel = REDIS_KEYS.ALL_MARKETS_CHANNEL;

    await subscriber.subscribe(channel);

    subscriber.on("message", (receivedChannel, message) => {
      if (receivedChannel === channel) {
        try {
          const data = JSON.parse(message);
          callback(data);
        } catch (error) {
          console.error("Error parsing Redis message:", error);
        }
      }
    });

    return async () => {
      await subscriber.unsubscribe(channel);
      subscriber.disconnect();
    };
  }
}
