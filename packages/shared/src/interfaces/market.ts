export interface Ticker {
  buy: string;
  sell: string;
  low: string;
  high: string;
  open: string;
  last: string;
  vol: string;
}

export interface MarketData {
  at: number;
  ticker: Ticker;
}

export interface MarketStats {
  last_price: string;
  lowest_ask: string;
  highest_bid: string;
  base_volume: string;
  quote_volume: string;
  price_change_percent_24h: string;
  highest_price_24h: string;
  lowest_price_24h: string;
}

export interface CombinedMarketData {
  currencyPair: string;
  buy: string;
  sell: string;
  low: string;
  high: string;
  open: string;
  last: string;
  vol: string;
  lastPrice: string;
  lowestAsk: string;
  highestBid: string;
  baseVolume: string;
  quoteVolume: string;
  priceChangePercent24h: string;
  highestPrice24h: string;
  lowestPrice24h: string;
  status: "gainer" | "loser" | "no change";
}

export interface AggregatedMarketData {
  period: string;
  averagePrice: number;
  minPrice: number;
  maxPrice: number;
  totalVolume: number;
  count: number;
  firstPrice: number;
  lastPrice: number;
}

// Socket.IO event interfaces
export interface SubscribeMessage {
  event: string;
  pair: string;
}

export interface MarketSubscription {
  pair: string;
  unsubscribe: () => Promise<void>;
}
