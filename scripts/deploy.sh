#!/bin/bash

# Crypto Market Watcher Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.yml"

if [ "$ENVIRONMENT" = "development" ]; then
    COMPOSE_FILE="docker-compose.dev.yml"
fi

echo -e "${GREEN}🚀 Starting deployment for ${ENVIRONMENT} environment${NC}"

# Function to check if required environment variables are set
check_env_vars() {
    echo -e "${YELLOW}📋 Checking environment variables...${NC}"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        required_vars=("API_KEY" "QUIDAX_BASE_URL")
        
        for var in "${required_vars[@]}"; do
            if [ -z "${!var}" ]; then
                echo -e "${RED}❌ Error: $var environment variable is not set${NC}"
                exit 1
            fi
        done
    fi
    
    echo -e "${GREEN}✅ Environment variables check passed${NC}"
}

# Function to build the application
build_app() {
    echo -e "${YELLOW}🔨 Building application...${NC}"
    
    # Install dependencies
    yarn install --frozen-lockfile
    
    # Build shared package first
    yarn workspace @crypto-watcher/shared build
    
    # Build applications
    yarn workspace @crypto-watcher/worker build
    yarn workspace @crypto-watcher/api build
    
    echo -e "${GREEN}✅ Application built successfully${NC}"
}

# Function to run tests
run_tests() {
    echo -e "${YELLOW}🧪 Running tests...${NC}"
    
    # Type checking
    yarn type-check
    
    # Run tests (when available)
    # yarn test
    
    echo -e "${GREEN}✅ Tests passed${NC}"
}

# Function to deploy with Docker Compose
deploy_docker() {
    echo -e "${YELLOW}🐳 Deploying with Docker Compose...${NC}"
    
    # Stop existing services
    docker-compose -f $COMPOSE_FILE down
    
    # Build and start services
    docker-compose -f $COMPOSE_FILE up --build -d
    
    echo -e "${GREEN}✅ Services deployed successfully${NC}"
}

# Function to check service health
check_health() {
    echo -e "${YELLOW}🏥 Checking service health...${NC}"
    
    # Wait for services to start
    sleep 30
    
    # Check Redis
    if docker-compose -f $COMPOSE_FILE exec redis redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis is healthy${NC}"
    else
        echo -e "${RED}❌ Redis health check failed${NC}"
    fi
    
    # Check MongoDB
    if docker-compose -f $COMPOSE_FILE exec mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ MongoDB is healthy${NC}"
    else
        echo -e "${RED}❌ MongoDB health check failed${NC}"
    fi
    
    # Check Worker (if production)
    if [ "$ENVIRONMENT" = "production" ]; then
        if curl -f http://localhost:3000/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Worker service is healthy${NC}"
        else
            echo -e "${YELLOW}⚠️  Worker health endpoint not available (this is expected if no health endpoint is implemented)${NC}"
        fi
        
        # Check API
        if curl -f http://localhost:8001/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ API service is healthy${NC}"
        else
            echo -e "${YELLOW}⚠️  API health endpoint not available (this is expected if no health endpoint is implemented)${NC}"
        fi
    fi
}

# Function to show deployment status
show_status() {
    echo -e "${YELLOW}📊 Deployment Status:${NC}"
    docker-compose -f $COMPOSE_FILE ps
    
    echo -e "\n${GREEN}🎉 Deployment completed successfully!${NC}"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        echo -e "${YELLOW}📡 Services are available at:${NC}"
        echo -e "  • Worker: http://localhost:3000"
        echo -e "  • API: http://localhost:8001"
    else
        echo -e "${YELLOW}📡 Development services are available at:${NC}"
        echo -e "  • Redis: localhost:6379"
        echo -e "  • MongoDB: localhost:27017"
        echo -e "  • Redis Commander: http://localhost:8081"
        echo -e "  • Mongo Express: http://localhost:8082 (admin/admin123)"
    fi
}

# Main deployment flow
main() {
    echo -e "${GREEN}🚀 Crypto Market Watcher Deployment${NC}"
    echo -e "${YELLOW}Environment: $ENVIRONMENT${NC}"
    echo -e "${YELLOW}Compose file: $COMPOSE_FILE${NC}\n"
    
    check_env_vars
    
    if [ "$ENVIRONMENT" = "production" ]; then
        build_app
        run_tests
    fi
    
    deploy_docker
    check_health
    show_status
}

# Run main function
main
