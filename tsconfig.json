{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "declaration": true, "declarationMap": true, "composite": true, "incremental": true, "baseUrl": ".", "paths": {"@crypto-watcher/shared": ["packages/shared/src"], "@crypto-watcher/shared/*": ["packages/shared/src/*"]}}, "references": [{"path": "./packages/shared"}, {"path": "./apps/worker"}, {"path": "./apps/api"}], "exclude": ["node_modules", "dist", "build"]}